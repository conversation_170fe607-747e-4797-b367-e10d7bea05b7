$nomention
$try
$reply
$if[$and[$or[$getServerVar[mathchannel]==$channelID;$getServerVar[backwardchannel]==$channelID;$getServerVar[decimalpointchannel]==$channelID]==true;$getVar[ban_user;$authorID]==true]==true]
$cooldown[10s;]
$addCmdReactions[⛔]
$sendMessage[⛔ <@$authorID> You have been banned from this bot.]
$stop
$endif
$textSplit[$getMessage[$channelID;$messageID;content]; ]
$var[msg;$splitText[1]]
$textSplit[$uptime;:]
$var[uptime2;$splitText[2]]

$if[$getServerVar[mathchannel]==$channelID]
$onlyIf[$isInteger[$var[msg]]==true;]

$sendMessage[🔍 DEBUG: User <@$authorID> | Current: $getServerVar[mathcountlimit] | Max: $getVar[maxcountlimit;$authorID] | Last: $getServerVar[mathlastUser]]
$if[$getServerVar[mathcountlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$getServerVar[mathanswer]]

$if[$getServerVar[mathlastUser]!=$authorID]
$setServerVar[mathcountlimit;0]
$setServerVar[mathlastUser;$authorID]
$endif

$setServerVar[mathcount;$sum[$getServerVar[mathcount];1]]
$setServerVar[mathcountlimit;$sum[$getServerVar[mathcountlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[mathcount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getVar[streak;$authorID]>$getVar[high-streak;$authorID]]
$setVar[high-streak;$getVar[streak;$authorID];$authorID]
$setVar[high-streak-Time;$getTimestamp;$authorID]
$endif
$if[$getUserVar[streak]>$getUserVar[high-streak]]
$setUserVar[high-streak;$getUserVar[streak]]
$setUserVar[high-streak-Time;$getTimestamp]
$endif

$if[$getServerVar[mathcount]>$getServerVar[mathhighscore]]
$if[$getServerVar[mathcountlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[mathhighscore;$getServerVar[mathcount]]
$setServerVar[totalhighscore;$sum[$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[mathhighscore];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[mathhighscore;$getServerVar[mathcount]]
$setServerVar[totalhighscore;$sum[$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[mathhighscore];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[mathcountlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$enableDecimals[no]
$var[num1;$optOff[$random[$sum[1;$getServerVar[mathcount]];$sum[10;$getServerVar[mathcount]]]]]
$var[num2;$randomText[+;-;*;/;%]]
$var[num3;$optOff[$random[1;11]]]
$if[$and[$var[num1]<$var[num3];$checkContains[$var[num2];-;%]==true]==true]
$var[num1;$sum[$var[num3];$var[num1];$random[5;20]]]
$elseif[$var[num2]==/]
$if[$getServerVar[mathcount]>9]
$var[num1;$sub[$var[num1];$calculate[$var[num1]%$var[num3]]]]
$else
$var[num1;$optOff[$random[10;21]]]
$var[num1;$sub[$var[num1];$calculate[$var[num1]%$var[num3]]]]
$endif
$endif
$setServerVar[mathcurrent;$var[num1] $var[num2] $var[num3]]
$setServerVar[mathanswer;$calculate[$getServerVar[mathcurrent]]]

$sendMessage[`$getServerVar[mathcount]`. Next number is **$getServerVar[mathcurrent]**]

$if[$getVar[backwardcount;$authorID]<4]
$if[$getVar[number-countlimit;$authorID]<=$getVar[userScores;$authorID]]
  $setVar[maxcountlimit;$sum[$getVar[maxcountlimit;$authorID];1];$authorID]

  $if[$getVar[number-countlimit;$authorID]==1000]
    $setVar[number-countlimit;5000;$authorID]
    $setVar[backwardcount;1;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==5000]
    $setVar[number-countlimit;10000;$authorID]
    $setVar[backwardcount;2;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==10000]
    $setVar[number-countlimit;50000;$authorID]
    $setVar[backwardcount;3;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==50000]
    $setVar[number-countlimit;100000;$authorID]
    $setVar[backwardcount;4;$authorID]
  $endif
$endif
$endif
$else
$if[$getServerVar[mathcooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[mathcooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[mathcount]==0]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mathcurrent]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mathcurrent]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next Number: **$getServerVar[mathanswer]**
Error: **User save used**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the correct number**. Next number is **$getServerVar[mathcurrent]**]

$stop
$elseif[0.99<$getServerVar[mathsave]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next Number: **$getServerVar[mathanswer]**
Error: **Channel save used**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[mathsave;$sub[$getServerVar[mathsave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** channel save! There are **$round[$getServerVar[mathsave];2]/$getServerVar[guildsaveslot]** saves left. **That's not the correct number**. Next number is **$getServerVar[mathcurrent]**]

$stop
$endif

$setServerVar[mathcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next Number: **$getServerVar[mathanswer]**
Error: **Ruined**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$var[num1;$optOff[$random[1;11]]]
$var[num2;$randomText[+;-;*;/;%]]
$var[num3;$optOff[$random[1;11]]]
$if[$and[$var[num1]<$var[num3];$checkContains[$var[num2];-;%]==true]==true]
$var[num1;$sum[$var[num3];$var[num1];$random[5;20]]]
$elseif[$var[num2]==/]
$var[num1;$optOff[$random[10;21]]]
$var[num1;$sub[$var[num1];$calculate[$var[num1]%$var[num3]]]]
$endif
$setServerVar[mathcurrent;$var[num1] $var[num2] $var[num3]]
$setServerVar[mathanswer;$calculate[$getServerVar[mathcurrent]]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[mathcount]**!! **That not the correct number**.]
$sendMessage[`0`. Next number is **$getServerVar[mathcurrent]**]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[mathcount;0]
$setServerVar[mathlastUser;]
$setServerVar[mathcountlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif
$else
$if[$getServerVar[mathcooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[mathcooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[mathcount]==0]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mathcurrent]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mathcurrent]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next Number: **$getServerVar[mathanswer]**
Error: **User save used | Count more than count limits**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **You can't count more than $getVar[maxcountlimit;$authorID]**]

$stop
$elseif[0.99<$getServerVar[mathsave]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next Number: **$getServerVar[mathanswer]**
Error: **Channel save used | Count more than count limits**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[mathsave;$sub[$getServerVar[mathsave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** channel save! There are **$round[$getServerVar[mathsave];2]/$getServerVar[guildsaveslot]** saves left. **You can't count more than $getVar[maxcountlimit;$authorID]**]

$stop
$endif
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next Number: **$getServerVar[mathanswer]**
Error: **Ruined | Count more than count limits**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$var[num1;$optOff[$random[1;11]]]
$var[num2;$randomText[+;-;*;/;%]]
$var[num3;$optOff[$random[1;11]]]
$if[$and[$var[num1]<$var[num3];$checkContains[$var[num2];-;%]==true]==true]
$var[num1;$sum[$var[num3];$var[num1];$random[5;20]]]
$elseif[$var[num2]==/]
$var[num1;$optOff[$random[10;21]]]
$var[num1;$sub[$var[num1];$calculate[$var[num1]%$var[num3]]]]
$endif
$setServerVar[mathcurrent;$var[num1] $var[num2] $var[num3]]
$setServerVar[mathanswer;$calculate[$getServerVar[mathcurrent]]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[mathcount]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**.]
$sendMessage[`0`. Next number is **$getServerVar[mathcurrent]**]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[mathcount;0]
$setServerVar[mathlastUser;]
$setServerVar[mathcountlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif

$elseif[$and[$getServerVar[backwardchannel]==$channelID;$getServerVar[backwardstart]==1]==true]
$onlyIf[$isInteger[$splitText[1]]==true;]

$if[$getServerVar[backwardcountlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$sub[$getServerVar[backwardcurrent];1]]

$if[$getServerVar[backwardlastUser]!=$authorID]
$setServerVar[backwardcountlimit;0]
$setServerVar[backwardlastUser;$authorID]
$endif

$setServerVar[backwardcurrent;$sub[$getServerVar[backwardcurrent];1]]
$setServerVar[backwardcountlimit;$sum[$getServerVar[backwardcountlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setServerVar[backwardcount;$sum[$getServerVar[backwardcount];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[mathcount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getVar[streak;$authorID]>$getVar[high-streak;$authorID]]
$setVar[high-streak;$getVar[streak;$authorID];$authorID]
$setVar[high-streak-Time;$getTimestamp;$authorID]
$endif
$if[$getUserVar[streak]>$getUserVar[high-streak]]
$setUserVar[high-streak;$getUserVar[streak]]
$setUserVar[high-streak-Time;$getTimestamp]
$endif

$if[$getServerVar[backwardcurrent]<$getServerVar[backwardcurrenthighscore]]
$if[$getServerVar[backwardcountlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$setServerVar[backwardcounthigh;$getServerVar[backwardcount]]
$setServerVar[totalhighscore;$sum[$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[mathhighscore];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$setServerVar[backwardcounthigh;$getServerVar[backwardcount]]
$setServerVar[totalhighscore;$sum[$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[mathhighscore];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[backwardcountlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$if[$getServerVar[backwardcurrent]==0]
$if[$getServerVar[backwardmulti]==0]
$setServerVar[backwardmulti;1]
$setServerVar[backwardcurrent;$sum[$multi[$getServerVar[backwardcurrentstart];5];1]]
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$sendMessage[You have counted **$getServerVar[backwardcurrentstart]** to **0**. Next number is **$sub[$getServerVar[backwardcurrent];1]**]
$setServerVar[backwardcurrentstart;$multi[$getServerVar[backwardcurrentstart];5]]
$else
$setServerVar[backwardmulti;0]
$setServerVar[backwardcurrent;$sum[$multi[$getServerVar[backwardcurrentstart];2];1]]
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$sendMessage[You have counted **$getServerVar[backwardcurrentstart]** to **0**. Next number is **$sub[$getServerVar[backwardcurrent];1]**]
$setServerVar[backwardcurrentstart;$multi[$getServerVar[backwardcurrentstart];2]]

$endif
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$endif

$if[$getVar[backwardcount;$authorID]<4]
$if[$getVar[number-countlimit;$authorID]<=$getVar[userScores;$authorID]]
  $setVar[maxcountlimit;$sum[$getVar[maxcountlimit;$authorID];1];$authorID]

  $if[$getVar[number-countlimit;$authorID]==1000]
    $setVar[number-countlimit;5000;$authorID]
    $setVar[backwardcount;1;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==5000]
    $setVar[number-countlimit;10000;$authorID]
    $setVar[backwardcount;2;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==10000]
    $setVar[number-countlimit;50000;$authorID]
    $setVar[backwardcount;3;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==50000]
    $setVar[number-countlimit;100000;$authorID]
    $setVar[backwardcount;4;$authorID]
  $endif
$endif
$endif
$else
$if[$getServerVar[backwardcooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[backwardcooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[backwardcurrent]==$sum[$getServerVar[backwardcurrentstart];1]]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$sub[$getServerVar[backwardcurrent];1]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$sub[$getServerVar[backwardcurrent];1]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**
Error: **User save used**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the next number**. Last number was **$getServerVar[backwardcurrent]**]

$stop
$elseif[0.99<$getServerVar[backwardsave]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**
Error: **Channel save used**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[backwardsave;$sub[$getServerVar[backwardsave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** channel save! There are **$round[$getServerVar[backwardsave];2]/$getServerVar[guildsaveslot]** saves left. **That's not the next number**. Last number was **$getServerVar[backwardcurrent]**]

$stop
$endif
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[backwardcount]**!! **That not the next number**.]
$sendMessage[High score: `$getServerVar[backwardcurrenthighscore]`. Next number is **$getServerVar[backwardcurrentstart]**]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**
Error: **Ruined**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[backwardcount;$sub[$getServerVar[backwardcount];$sub[$getServerVar[backwardcurrentstart];$getServerVar[backwardcurrent];1]]]
$setServerVar[backwardcurrent;$sum[$getServerVar[backwardcurrentstart];1]]
$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[backwardcountlimit;0]
$setServerVar[backwardlastUser;]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif
$else
$if[$getServerVar[backwardcooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[backwardcooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[backwardcurrent]==$sum[$getServerVar[backwardcurrentstart];1]]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Wait someone else send **$sub[$getServerVar[backwardcurrent];1]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Wait someone else send **$sub[$getServerVar[backwardcurrent];1]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**
Error: **User save used | Count more than count limits**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **You can't count more than $getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[backwardcurrent]**]

$stop
$elseif[0.99<$getServerVar[backwardsave]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**
Error: **Channel save used | Count more than count limits**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[backwardsave;$sub[$getServerVar[backwardsave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** channel save! There are **$round[$getServerVar[backwardsave];2]/$getServerVar[guildsaveslot]** saves left. **You can't count more than $getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[backwardcurrent]**]

$stop
$endif
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**
Error: **Ruined | Count more than count limits**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[backwardcount]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**.]
$sendMessage[High score: `$getServerVar[backwardcurrenthighscore]`. Next number is **$getServerVar[backwardcurrentstart]**]

$setServerVar[backwardcount;$sub[$getServerVar[backwardcount];$sub[$getServerVar[backwardcurrentstart];$getServerVar[backwardcurrent];1]]]
$setServerVar[backwardcurrent;$sum[$getServerVar[backwardcurrentstart];1]]
$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[backwardcountlimit;0]
$setServerVar[backwardlastUser;]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif


$elseif[$channelID==$getServerVar[decimalpointchannel]]
$enableDecimals[yes]
$onlyIf[$isNumber[$var[msg]]==true;]

$var[msg;$round[$var[msg];10]]

$if[$getServerVar[decimalpoint-countlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]]

$async[decimalcode]
$if[$getServerVar[decimalpoint-lastUser]!=$authorID]
$setServerVar[decimalpoint-countlimit;0]
$setServerVar[decimalpoint-lastUser;$authorID]
$endif

$setServerVar[decimalpoint-count;$sum[$getServerVar[decimalpoint-count];1]]
$setServerVar[decimalpoint-countlimit;$sum[$getServerVar[decimalpoint-countlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[mathcount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getVar[streak;$authorID]>$getVar[high-streak;$authorID]]
$setVar[high-streak;$getVar[streak;$authorID];$authorID]
$setVar[high-streak-Time;$getTimestamp;$authorID]
$endif
$if[$getUserVar[streak]>$getUserVar[high-streak]]
$setUserVar[high-streak;$getUserVar[streak]]
$setUserVar[high-streak-Time;$getTimestamp]
$endif

$if[$getServerVar[decimalpoint-counthigh]<$getServerVar[decimalpoint-count]]
$if[$getServerVar[decimalpoint-countlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[decimalpoint-counthigh;$sum[$getServerVar[decimalpoint-counthigh];1]]
$setServerVar[decimalpoint-high;$round[$var[msg];10]]
$setServerVar[totalhighscore;$sum[$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[mathhighscore];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[decimalpoint-counthigh;$sum[$getServerVar[decimalpoint-counthigh];1]]
$setServerVar[decimalpoint-high;$round[$var[msg];10]]
$setServerVar[totalhighscore;$sum[$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[mathhighscore];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[decimalpoint-countlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$setServerVar[decimalpoint-current;$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]]

$if[$getServerVar[decimalpoint-current]>=$getServerVar[decimalpoint-end]]

$setServerVar[decimalpoint-current;0]
$setServerVar[decimalpoint-num;$multi[$getServerVar[decimalpoint-num];0.1]]
$setServerVar[decimalpoint-end;1]
$setServerVar[decimalpoint-end;$round[$sub[$getServerVar[decimalpoint-end];$getServerVar[decimalpoint-num]];10]]
$endif
$endasync

$if[$getVar[backwardcount;$authorID]<4]
$if[$getVar[number-countlimit;$authorID]<=$getVar[userScores;$authorID]]
  $setVar[maxcountlimit;$sum[$getVar[maxcountlimit;$authorID];1];$authorID]

  $if[$getVar[number-countlimit;$authorID]==1000]
    $setVar[number-countlimit;5000;$authorID]
    $setVar[backwardcount;1;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==5000]
    $setVar[number-countlimit;10000;$authorID]
    $setVar[backwardcount;2;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==10000]
    $setVar[number-countlimit;50000;$authorID]
    $setVar[backwardcount;3;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==50000]
    $setVar[number-countlimit;100000;$authorID]
    $setVar[backwardcount;4;$authorID]
  $endif
$endif
$endif
$else
$if[$getServerVar[decimalpoint-cooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[decimalpoint-cooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[decimalpoint-count]==0]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **0.1**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**
Error: **User save used**

Global Stats:
❌ $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
❌ $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the next number**. Last number was **$getServerVar[decimalpoint-current]**]

$stop
$elseif[0.99<$getServerVar[decimalpoint-save]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**
Error: **Channel save used**

Global Stats:
❌ $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
❌ $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[decimalpoint-save;$sub[$getServerVar[decimalpoint-save];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** channel save! There are **$round[$getServerVar[decimalpoint-save];2]/$getServerVar[guildsaveslot]** saves left. **That's not the next number**. Last number was **$getServerVar[decimalpoint-current]**]

$stop
$endif
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[decimalpoint-count]**!! **That's not the next number**. Next number is **0.1**]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**
Error: **Ruined**

Global Stats:
❌ $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
❌ $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[decimalpoint-count;0]
$setServerVar[decimalpoint-current;0]
$setServerVar[decimalpoint-num;0.1]
$setServerVar[decimalpoint-end;0.9]
$setServerVar[decimalpoint-lastUser;]
$setServerVar[decimalpoint-countlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif
$else
$if[$getServerVar[decimalpoint-cooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[decimalpoint-cooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[decimalpoint-count]==0]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **0.1**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**
Error: **User save used | Count more than count limits**

Global Stats:
❌ $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
❌ $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[decimalpoint-current]**]

$stop
$elseif[0.99<$getServerVar[decimalpoint-save]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**
Error: **Channel save used | Count more than count limits**

Global Stats:
❌ $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
❌ $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[decimalpoint-save;$sub[$getServerVar[decimalpoint-save];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** channel save! There are **$round[$getServerVar[decimalpoint-save];2]/$getServerVar[guildsaveslot]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[decimalpoint-current]**]

$stop
$endif
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[decimalpoint-count]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**. Next number is **0.1**]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**
Error: **Ruined | Count more than count limits**

Global Stats:
❌ $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
❌ $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[decimalpoint-count;0]
$setServerVar[decimalpoint-current;0]
$setServerVar[decimalpoint-num;0.1]
$setServerVar[decimalpoint-end;0.9]
$setServerVar[decimalpoint-lastUser;]
$setServerVar[decimalpoint-countlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif

$endif
$catch
$stop
$endtry