$nomention
$try
$reply
$if[$and[$getServerVar[base64channel]==$channelID;$getVar[ban_user;$authorID]==true]==true]
$cooldown[10s;]
$addCmdReactions[⛔]
$sendMessage[⛔ <@$authorID> You have been banned from this bot.]
$stop
$endif
$textSplit[$getMessage[$channelID;$messageID;content]; ]
$var[msg;$splitText[1]]
$textSplit[$uptime;:]
$var[uptime;$splitText[2]]

$if[$getServerVar[base64channel]==$channelID]

$if[$getServerVar[base64countlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$getServerVar[base64-base]]

$if[$getServerVar[base64lastUser]!=$authorID]
$setServerVar[base64countlimit;0]
$setServerVar[base64lastUser;$authorID]
$endif

$textSplit[ /1/2/3/4/5/6/7/8/9/a/b/c/d/e/f/g/h/i/j/k/l/m/n/o/p/q/r/s/t/u/v/w/x/y/z/A/B/C/D/E/F/G/H/I/J/K/L/M/N/O/P/Q/R/S/T/U/V/W/X/Y/Z/+/-/0/0;/]
$var[1;$splitText[$getServerVar[base64-1]]]
$var[2;$splitText[$getServerVar[base64-2]]]
$var[3;$splitText[$getServerVar[base64-3]]]
$var[4;$splitText[$getServerVar[base64-4]]]

$setServerVar[base64-base;$var[4]$var[3]$var[2]$var[1]]

$setServerVar[base64-1;$sum[$getServerVar[base64-1];1]]

$if[$getServerVar[base64-1]==65]
$setServerVar[base64-2;$sum[$getServerVar[base64-2];1]]
$endif
$if[$getServerVar[base64-1]>=66]
$setServerVar[base64-1;2]
$endif

$if[$getServerVar[base64-2]==65]
$setServerVar[base64-3;$sum[$getServerVar[base64-3];1]]
$setServerVar[base64-2;$sum[$getServerVar[base64-2];1]]
$endif
$if[$getServerVar[base64-2]>=67]
$setServerVar[base64-2;2]
$endif
$if[$getServerVar[base64-3]==65]
$setServerVar[base64-4;$sum[$getServerVar[base64-4];1]]
$setServerVar[base64-3;$sum[$getServerVar[base64-3];1]]
$endif
$if[$getServerVar[base64-3]>=67]
$setServerVar[base64-3;2]
$endif

$setServerVar[base64count;$sum[$getServerVar[base64count];1]]
$setServerVar[base64-current;$var[msg]]
$setServerVar[base64countlimit;$sum[$getServerVar[base64countlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[mathcount];$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getServerVar[base64count]>$getServerVar[base64counthigh]]
$if[$getServerVar[base64countlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[base64counthigh;$getServerVar[base64count]]
$setServerVar[base64-high;$var[msg]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[base64counthigh;$getServerVar[base64count]]
$setServerVar[base64-high;$var[msg]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[base64countlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$else
$if[$getServerVar[base64cooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[base64cooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[base64count]==0]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **1**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[base64-base]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the next number**. Last number was **$getServerVar[base64-current]**]

$stop
$elseif[0.99<$getServerVar[base64save]]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[base64save;$sub[$getServerVar[base64save];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[base64save];2]/$getServerVar[guildsaveslot]** saves left. **That's not the next number**. Last number was **$getServerVar[base64-current]**]
$stop
$endif

$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[base64count]**!! **That's not the next number**. Next number is **1**]

$addCmdReactions[❌]
$setServerVar[base64count;0]
$setServerVar[base64-base;1]
$setServerVar[base64-1;3]
$setServerVar[base64-2;1]
$setServerVar[base64-3;1]
$setServerVar[base64-4;1]
$setServerVar[base64lastUser;]
$setServerVar[base64countlimit;0]

$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$endif
$else
$if[$getServerVar[base64cooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[base64cooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[base64count]==0]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **1**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[base64-base]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[base64-current]**]

$stop
$elseif[0.99<$getServerVar[base64save]]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[base64save;$sub[$getServerVar[base64save];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[base64save];2]/$getServerVar[guildsaveslot]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[base64-current]**]
$stop
$endif

$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[base64count]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**. Next number is **1**]

$addCmdReactions[❌]
$setServerVar[base64count;0]
$setServerVar[base64-base;1]
$setServerVar[base64-1;3]
$setServerVar[base64-2;1]
$setServerVar[base64-3;1]
$setServerVar[base64-4;1]
$setServerVar[base64lastUser;]
$setServerVar[base64countlimit;0]

$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$endif

$endif
$catch
$stop
$endtry