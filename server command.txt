$nomention
$color[AC4CE4]
$if[$guildID==]
$description[You can't use this on DMs!]
$stop
$endif

$if[$and[$message[mode]!=;$message[stats]!=]==true]
$ephemeral
$title[Error.]
$description[Please select one option.]
$stop
$endif

$if[$and[$message[mode]==;$message[stats]==]==true]
$if[$channelID==$getServerVar[mathchannel]]
$var[server;Mode: **Maths**
Channel: <#$getServerVar[mathchannel]>
Current Number: **$getServerVar[mathcount]**
High Score: **$getServerVar[mathhighscore]**
Base Next Number: **$getServerVar[mathcurrent]**
Channel Saves: **$round[$getServerVar[mathsave];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[mathlastUser]> (Counted $getServerVar[mathcountlimit] times)]
$elseif[$channelID==$getServerVar[backwardchannel]]
$var[server;Mode: **Backwards**
Channel: <#$getServerVar[backwardchannel]>
Base Current Start: **$getServerVar[backwardcurrentstart]**
Base Current: **$getServerVar[backwardcurrent]** ($getServerVar[backwardcount])
Base High Score: **$getServerVar[backwardcurrenthighscore]** ($getServerVar[backwardcounthigh])
Channel Saves: **$round[$getServerVar[backwardsave];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[backwardlastUser]> (Counted $getServerVar[backwardcountlimit] times)]
$elseif[$channelID==$getServerVar[sequencechannel]]
$var[server;Mode: **Sequences**
Channel: <#$getServerVar[sequencechannel]>
Base Current: **$getServerVar[sequence-currentbase]($getServerVar[sequence-limitbase])** ($getServerVar[sequencecount])
Base High Score: **$getServerVar[sequence-highbase]** ($getServerVar[sequencecounthigh])
Channel Saves: **$round[$getServerVar[sequencesave];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[sequencelastUser]> (Counted $getServerVar[sequencecountlimit] times)]
$elseif[$channelID==$getServerVar[decimalpointchannel]]
$var[server;Mode: **Decimal Point**
Channel: <#$getServerVar[decimalpointchannel]>
Base Current: **$getServerVar[decimalpoint-current]** ($getServerVar[decimalpoint-count])
Base High Score: **$getServerVar[decimalpoint-high]** ($getServerVar[decimalpoint-counthigh])
Channel Saves: **$round[$getServerVar[decimalpoint-save];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[decimalpoint-lastUser]> (Counted $getServerVar[decimalpoint-countlimit] times)]
$elseif[$channelID==$getServerVar[mayanchannel]]
$var[server;Mode: **Mayan**
Channel: <#$getServerVar[mayanchannel]>
Base Current: **$getServerVar[mayan-base-current]** ($getServerVar[mayancount])
Base High Score: **$getServerVar[mayan-basehigh]** ($getServerVar[mayancounthigh])
Channel Saves: **$round[$getServerVar[mayansave];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[mayanlastUser]> (Counted $getServerVar[mayancountlimit] times)]
$elseif[$channelID==$getServerVar[base64channel]]
$var[server;Mode: **Base-64**
Channel: <#$getServerVar[base64channel]>
Base Current: **$getServerVar[base64-current]** ($getServerVar[base64count])
Base High Score: **$getServerVar[base64-high]** ($getServerVar[base64counthigh])
Channel Saves: **$round[$getServerVar[base64save];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[base64lastUser]> (Counted $getServerVar[base64countlimit] times)]
$else
$ephemeral
$title[Error.]
$description[The channel is not counting channel.]
$stop
$endif
$endif

$if[$message[stats]==]
$if[$message[mode]==math]
$var[server;Mode: **Maths**
Channel: <#$getServerVar[mathchannel]>
Current Number: **$getServerVar[mathcount]**
High Score: **$getServerVar[mathhighscore]**
Base Next Number: **$getServerVar[mathcurrent]**
Channel Saves: **$round[$getServerVar[mathsave];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[mathlastUser]> (Counted $getServerVar[mathcountlimit] times)]
$elseif[$message[mode]==backward]
$var[server;Mode: **Backwards**
Channel: <#$getServerVar[backwardchannel]>
Base Current Start: **$getServerVar[backwardcurrentstart]**
Base Current: **$getServerVar[backwardcurrent]** ($getServerVar[backwardcount])
Base High Score: **$getServerVar[backwardcurrenthighscore]** ($getServerVar[backwardcounthigh])
Channel Saves: **$round[$getServerVar[backwardsave];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[backwardlastUser]> (Counted $getServerVar[backwardcountlimit] times)]
$elseif[$message[mode]==sequence]
$var[server;Mode: **Sequences**
Channel: <#$getServerVar[sequencechannel]>
Base Current: **$getServerVar[sequence-currentbase]($getServerVar[sequence-limitbase])** ($getServerVar[sequencecount])
Base High Score: **$getServerVar[sequence-highbase]** ($getServerVar[sequencecounthigh])
Channel Saves: **$round[$getServerVar[sequencesave];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[sequencelastUser]> (Counted $getServerVar[sequencecountlimit] times)]
$elseif[$message[mode]==decimalpoint]
$var[server;Mode: **Decimal Point**
Channel: <#$getServerVar[decimalpointchannel]>
Base Current: **$getServerVar[decimalpoint-current]** ($getServerVar[decimalpoint-count])
Base High Score: **$getServerVar[decimalpoint-high]** ($getServerVar[decimalpoint-counthigh])
Channel Saves: **$round[$getServerVar[decimalpoint-save];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[decimalpoint-lastUser]> (Counted $getServerVar[decimalpoint-countlimit] times)]
$elseif[$message[mode]==mayan]
$var[server;Mode: **Mayan**
Channel: <#$getServerVar[mayanchannel]>
Base Current: **$getServerVar[mayan-base-current]** ($getServerVar[mayancount])
Base High Score: **$getServerVar[mayan-basehigh]** ($getServerVar[mayancounthigh])
Channel Saves: **$round[$getServerVar[mayansave];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[mayanlastUser]> (Counted $getServerVar[mayancountlimit] times)]
$elseif[$message[mode]==base64]
$var[server;Mode: **Base-64**
Channel: <#$getServerVar[base64channel]>
Base Current: **$getServerVar[base64-current]** ($getServerVar[base64count])
Base High Score: **$getServerVar[base64-high]** ($getServerVar[base64counthigh])
Channel Saves: **$round[$getServerVar[base64save];2]/$getServerVar[guildsaveslot]**
Last counted by: <@$getServerVar[base64lastUser]> (Counted $getServerVar[base64countlimit] times)]
$endif
$description[$var[server]]
$elseif[$message[mode]==]

$if[$message[stats]==scores]
$var[1;Current Number: **$getServerVar[mathcount]**
High Score: **$getServerVar[mathhighscore]**]
$var[2;Base Current Start: **$getServerVar[backwardcurrentstart]**
Base Current: **$getServerVar[backwardcurrent]** ($getServerVar[backwardcount])
Base High Score: **$getServerVar[backwardcurrenthighscore]** ($getServerVar[backwardcounthigh])]
$var[3;Base Current: **$getServerVar[sequence-currentbase-count]** ($getServerVar[sequencecount])
Base High Score: **$getServerVar[sequence-highbase]** ($getServerVar[sequencecounthigh])]
$var[4;Base Current: **$getServerVar[decimalpoint-current]** ($getServerVar[decimalpoint-count])
Base High Score: **$getServerVar[decimalpoint-high]** ($getServerVar[decimalpoint-counthigh])]
$var[8;Base Current: **$getServerVar[mayan-base-current]** ($getServerVar[mayancount])
Base High Score: **$getServerVar[mayan-basehigh]** ($getServerVar[mayancounthigh])]
$var[9;Base Current: **$getServerVar[base64-current]** ($getServerVar[base64count])
Base High Score: **$getServerVar[base64-high]** ($getServerVar[base64counthigh])]
$var[stats;Scores]
$var[6;$getServerVar[totalcurrentcount]]
$var[7;Total Current Scores:]
$var[10;__ __
Total High Score: **$getServerVar[totalhighscore]**]
$elseif[$message[stats]==channelsaves]
$enableDecimals[yes]
$var[1;$round[$getServerVar[mathsave];2]/$getServerVar[guildsaveslot]]
$var[2;$round[$getServerVar[backwardsave];2]/$getServerVar[guildsaveslot]]
$var[3;$round[$getServerVar[sequencesave];2]/$getServerVar[guildsaveslot]]
$var[4;$round[$getServerVar[decimalpoint-save];2]/$getServerVar[guildsaveslot]]
$var[8;$round[$getServerVar[mayansave];2]/$getServerVar[guildsaveslot]]
$var[9;$round[$getServerVar[base64save];2]/$getServerVar[guildsaveslot]]
$var[stats;Channel Saves]
$var[6;$round[$sum[$getServerVar[backwardsave];$getServerVar[sequencesave];$getServerVar[mathsave];$getServerVar[decimalpoint-save];$getServerVar[mayansave];$getServerVar[base64save]];2]]
$var[7;Total Saves:]
$elseif[$message[stats]==lastUser]
$var[1;Last counted by: <@$getServerVar[mathlastUser]> (Counted $getServerVar[mathcountlimit] times)]
$var[2;Last counted by: <@$getServerVar[backwardlastUser]> (Counted $getServerVar[backwardcountlimit] times)]
$var[3;Last counted by: <@$getServerVar[sequencelastUser]> (Counted $getServerVar[sequencecountlimit] times)]
$var[4;Last counted by: <@$getServerVar[decimalpoint-lastUser]> (Counted $getServerVar[decimalpoint-countlimit] times)]
$var[8;Last counted by: <@$getServerVar[mayanlastUser]> (Counted $getServerVar[mayancountlimit] times)]
$var[9;Last counted by: <@$getServerVar[base64lastUser]> (Counted $getServerVar[base64countlimit] times)]
$var[stats;Last User]
$endif

$var[5;0]

$if[$getServerVar[mathchannel]!=]
$addField[**__Math__**;<#$getServerVar[mathchannel]>
$var[1];yes]
$var[5;$sum[$var[5];1]]
$endif
$if[$getServerVar[backwardchannel]!=]
$addField[**__Backward__**;<#$getServerVar[backwardchannel]>
$var[2];yes]
$var[5;$sum[$var[5];1]]
$endif
$if[$getServerVar[sequencechannel]!=]
$addField[**__Sequence__**;<#$getServerVar[sequencechannel]>
$var[3];yes]
$var[5;$sum[$var[5];1]]
$endif
$if[$getServerVar[decimalpointchannel]!=]
$addField[**__Decimal Point__**;<#$getServerVar[decimalpointchannel]>
$var[4];yes]
$var[5;$sum[$var[5];1]]
$endif
$if[$getServerVar[mayanchannel]!=]
$addField[**__Mayan__**;<#$getServerVar[mayanchannel]>
$var[8];yes]
$var[5;$sum[$var[5];1]]
$endif
$if[$getServerVar[base64channel]!=]
$addField[**__Base-64__**;<#$getServerVar[base64channel]>
$var[9];yes]
$var[5;$sum[$var[5];1]]
$endif

$description[Channel Count: **$var[5]**
$if[$message[stats]!=lastUser]$var[7] **$var[6]**$var[10]$else Stat: **$toTitleCase[$var[stats]]**$endif
$if[$message[stats]!=lastUser]Stat: **$toTitleCase[$var[stats]]**$endif]
$endif

$author[$serverName[$guildID]]
$authorIcon[$serverIcon]
$footer[/help]