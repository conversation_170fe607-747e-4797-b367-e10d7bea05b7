$nomention
$try
$reply
$if[$and[$or[$getServerVar[mathchannel]==$channelID;$getServerVar[backwardchannel]==$channelID;$getServerVar[sequencechannel]==$channelID;$getServerVar[base64channel]==$channelID]==true;$getVar[ban_user;$authorID]==true]==true]
$cooldown[10s;]
$addCmdReactions[⛔]
$sendMessage[⛔ <@$authorID> You have been banned from this bot.]
$stop
$endif

$textSplit[$getMessage[$channelID;$messageID;content]; ]
$var[msg;$splitText[1]]
$textSplit[$uptime;:]
$var[uptime2;$splitText[2]]

$if[$and[$getServerVar[mathchannel]==$channelID;$getServerVar[mathstart]==1]==true]
$onlyIf[$isInteger[$var[msg]]==true;]

$if[$getServerVar[mathcountlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$getServerVar[mathanswer]]

$if[$getServerVar[mathlastUser]!=$authorID]
$setServerVar[mathcountlimit;0]
$setServerVar[mathlastUser;$authorID]
$endif

$setServerVar[mathcount;$sum[$getServerVar[mathcount];1]]
$setServerVar[mathcountlimit;$sum[$getServerVar[mathcountlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[mathcount];$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getVar[streak;$authorID]>$getVar[high-streak;$authorID]]
$setVar[high-streak;$getVar[streak;$authorID];$authorID]
$setVar[high-streak-Time;$getTimestamp;$authorID]
$endif
$if[$getUserVar[streak]>$getUserVar[high-streak]]
$setUserVar[high-streak;$getUserVar[streak]]
$setUserVar[high-streak-Time;$getTimestamp]
$endif

$if[$getServerVar[mathcount]>$getServerVar[mathhighscore]]
$if[$getServerVar[mathcountlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[mathhighscore;$getServerVar[mathcount]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[mathhighscore;$getServerVar[mathcount]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[mathcountlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$enableDecimals[no]
$var[num1;$optOff[$random[$sum[1;$getServerVar[mathcount]];$sum[10;$getServerVar[mathcount]]]]]
$var[num2;$randomText[+;-;*;/;%]]
$var[num3;$optOff[$random[1;11]]]
$if[$and[$var[num1]<$var[num3];$checkContains[$var[num2];-;%]==true]==true]
$var[num1;$sum[$var[num3];$var[num1];$random[5;20]]]
$elseif[$var[num2]==/]
$if[$getServerVar[mathcount]>9]
$var[num1;$sub[$var[num1];$calculate[$var[num1]%$var[num3]]]]
$else
$var[num1;$optOff[$random[10;21]]]
$var[num1;$sub[$var[num1];$calculate[$var[num1]%$var[num3]]]]
$endif
$endif
$setServerVar[mathcurrent;$var[num1] $var[num2] $var[num3]]
$setServerVar[mathanswer;$calculate[$getServerVar[mathcurrent]]]

$sendMessage[`$getServerVar[mathcount]`. Next number is **$getServerVar[mathcurrent]**]

$if[$getVar[backwardcount;$authorID]<4]
$if[$getVar[number-countlimit;$authorID]<=$getVar[userScores;$authorID]]
  $setVar[maxcountlimit;$sum[$getVar[maxcountlimit;$authorID];1];$authorID]

  $if[$getVar[number-countlimit;$authorID]==1000]
    $setVar[number-countlimit;5000;$authorID]
    $setVar[backwardcount;1;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==5000]
    $setVar[number-countlimit;10000;$authorID]
    $setVar[backwardcount;2;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==10000]
    $setVar[number-countlimit;50000;$authorID]
    $setVar[backwardcount;3;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==50000]
    $setVar[number-countlimit;100000;$authorID]
    $setVar[backwardcount;4;$authorID]
  $endif
$endif
$endif
$else
$if[$getServerVar[mathcooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[mathcooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[mathcount]==0]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mathcurrent]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mathcurrent]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next number: **$getServerVar[mathanswer]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the correct number**. Next number is **$getServerVar[mathcurrent]**]


$stop
$elseif[0.99<$getServerVar[mathsave]]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next number: **$getServerVar[mathanswer]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[mathsave;$sub[$getServerVar[mathsave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[mathsave];2]/$getServerVar[guildsaveslot]** saves left. **That's not the correct number**. Next number is **$getServerVar[mathcurrent]**]

$stop
$endif
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next number: **$getServerVar[mathanswer]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$var[num1;$optOff[$random[1;11]]]
$var[num2;$randomText[+;-;*;/;%]]
$var[num3;$optOff[$random[1;11]]]
$if[$and[$var[num1]<$var[num3];$checkContains[$var[num2];-;%]==true]==true]
$var[num1;$sum[$var[num3];$var[num1];$random[5;20]]]
$elseif[$var[num2]==/]
$var[num1;$optOff[$random[10;21]]]
$var[num1;$sub[$var[num1];$calculate[$var[num1]%$var[num3]]]]
$endif
$setServerVar[mathcurrent;$var[num1] $var[num2] $var[num3]]
$setServerVar[mathanswer;$calculate[$getServerVar[mathcurrent]]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[mathcount]**!! **That not the correct number**.]
$sendMessage[`0`. Next number is **$getServerVar[mathcurrent]**]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[mathcount;0]
$setServerVar[mathlastUser;]
$setServerVar[mathcountlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif
$else
$if[$getServerVar[mathcooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[mathcooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[mathcount]==0]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mathcurrent]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$elseif[$var[uptime2]==00]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mathcurrent]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next number: **$getServerVar[mathanswer]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Next number is **$getServerVar[mathcurrent]**]

$stop
$elseif[0.99<$getServerVar[mathsave]]
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next number: **$getServerVar[mathanswer]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[mathsave;$sub[$getServerVar[mathsave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[mathsave];2]/$getServerVar[guildsaveslot]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Next number is **$getServerVar[mathcurrent]**]

$stop
$endif
$setServerVar[mathcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Math
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Current Math: **$getServerVar[mathcurrent]**
Base Next number: **$getServerVar[mathanswer]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$var[num1;$optOff[$random[1;11]]]
$var[num2;$randomText[+;-;*;/;%]]
$var[num3;$optOff[$random[1;11]]]
$if[$and[$var[num1]<$var[num3];$checkContains[$var[num2];-;%]==true]==true]
$var[num1;$sum[$var[num3];$var[num1];$random[5;20]]]
$elseif[$var[num2]==/]
$var[num1;$optOff[$random[10;21]]]
$var[num1;$sub[$var[num1];$calculate[$var[num1]%$var[num3]]]]
$endif
$setServerVar[mathcurrent;$var[num1] $var[num2] $var[num3]]
$setServerVar[mathanswer;$calculate[$getServerVar[mathcurrent]]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[mathcount]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**.]
$sendMessage[`0`. Next number is **$getServerVar[mathcurrent]**]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[mathcount;0]
$setServerVar[mathlastUser;]
$setServerVar[mathcountlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif



$elseif[$and[$getServerVar[backwardchannel]==$channelID;$getServerVar[backwardstart]==1]==true]
$onlyIf[$isInteger[$splitText[1]]==true;]

$if[$getServerVar[backwardcountlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$sub[$getServerVar[backwardcurrent];1]]

$if[$getServerVar[backwardlastUser]!=$authorID]
$setServerVar[backwardcountlimit;0]
$setServerVar[backwardlastUser;$authorID]
$endif

$setServerVar[backwardcurrent;$sub[$getServerVar[backwardcurrent];1]]
$setServerVar[backwardcountlimit;$sum[$getServerVar[backwardcountlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setServerVar[backwardcount;$sum[$getServerVar[backwardcount];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[mathcount];$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getVar[streak;$authorID]>$getVar[high-streak;$authorID]]
$setVar[high-streak;$getVar[streak;$authorID];$authorID]
$setVar[high-streak-Time;$getTimestamp;$authorID]
$endif
$if[$getUserVar[streak]>$getUserVar[high-streak]]
$setUserVar[high-streak;$getUserVar[streak]]
$setUserVar[high-streak-Time;$getTimestamp]
$endif

$if[$getServerVar[backwardcurrent]<$getServerVar[backwardcurrenthighscore]]
$if[$getServerVar[backwardcountlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$setServerVar[backwardcounthigh;$getServerVar[backwardcount]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$setServerVar[backwardcounthigh;$getServerVar[backwardcount]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[backwardcountlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$if[$getServerVar[backwardcurrent]==0]
$if[$getServerVar[backwardmulti]==0]
$setServerVar[backwardmulti;1]
$setServerVar[backwardcurrent;$sum[$multi[$getServerVar[backwardcurrentstart];5];1]]
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$sendMessage[You have counted **$getServerVar[backwardcurrentstart]** to **0**. Next number is **$sub[$getServerVar[backwardcurrent];1]**]
$setServerVar[backwardcurrentstart;$multi[$getServerVar[backwardcurrentstart];5]]
$else
$setServerVar[backwardmulti;0]
$setServerVar[backwardcurrent;$sum[$multi[$getServerVar[backwardcurrentstart];2];1]]
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$sendMessage[You have counted **$getServerVar[backwardcurrentstart]** to **0**. Next number is **$sub[$getServerVar[backwardcurrent];1]**]
$setServerVar[backwardcurrentstart;$multi[$getServerVar[backwardcurrentstart];2]]

$endif
$setServerVar[backwardcurrenthighscore;$getServerVar[backwardcurrent]]
$endif

$if[$getVar[backwardcount;$authorID]<4]
$if[$getVar[number-countlimit;$authorID]<=$getVar[userScores;$authorID]]
  $setVar[maxcountlimit;$sum[$getVar[maxcountlimit;$authorID];1];$authorID]

  $if[$getVar[number-countlimit;$authorID]==1000]
    $setVar[number-countlimit;5000;$authorID]
    $setVar[backwardcount;1;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==5000]
    $setVar[number-countlimit;10000;$authorID]
    $setVar[backwardcount;2;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==10000]
    $setVar[number-countlimit;50000;$authorID]
    $setVar[backwardcount;3;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==50000]
    $setVar[number-countlimit;100000;$authorID]
    $setVar[backwardcount;4;$authorID]
  $endif
$endif
$endif
$else
$if[$getServerVar[backwardcooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[backwardcooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[backwardcurrent]==$sum[$getServerVar[backwardcurrentstart];1]]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$sub[$getServerVar[backwardcurrent];1]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$sub[$getServerVar[backwardcurrent];1]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the next number**. Last number was **$getServerVar[backwardcurrent]**]

$stop
$elseif[0.99<$getServerVar[backwardsave]]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[backwardsave;$sub[$getServerVar[backwardsave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[backwardsave];2]/$getServerVar[guildsaveslot]** saves left. **That's not the next number**. Last number was **$getServerVar[backwardcurrent]**]

$stop
$endif
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[backwardcount]**!! **That not the next number**.]
$sendMessage[High score: `$getServerVar[backwardcounthigh]`. Next number is **$getServerVar[backwardcurrentstart]**]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[backwardcount;$sub[$getServerVar[backwardcount];$sub[$getServerVar[backwardcurrentstart];$getServerVar[backwardcurrent];1]]]
$setServerVar[backwardcurrent;$sum[$getServerVar[backwardcurrentstart];1]]
$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[backwardcountlimit;0]
$setServerVar[backwardlastUser;]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif
$else
$if[$getServerVar[backwardcooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[backwardcooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[backwardcurrent]==$sum[$getServerVar[backwardcurrentstart];1]]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Wait someone else send **$sub[$getServerVar[backwardcurrent];1]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$sub[$getServerVar[backwardcurrent];1]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[backwardcurrent]**]

$stop
$elseif[0.99<$getServerVar[backwardsave]]
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[backwardsave;$sub[$getServerVar[backwardsave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[backwardsave];2]/$getServerVar[guildsaveslot]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[backwardcurrent]**]

$stop
$endif
$setServerVar[backwardcooldown;$sum[$getTimestamp;10]]
$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[backwardcount]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**.]
$sendMessage[High score: `$getServerVar[backwardcounthigh]`. Next number is **$getServerVar[backwardcurrentstart]**]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Backward
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sub[$getServerVar[backwardcurrent];1]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[backwardcount;$sub[$getServerVar[backwardcount];$sub[$getServerVar[backwardcurrentstart];$getServerVar[backwardcurrent];1]]]
$setServerVar[backwardcurrent;$sum[$getServerVar[backwardcurrentstart];1]]
$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[backwardcountlimit;0]
$setServerVar[backwardlastUser;]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif



$elseif[$and[$getServerVar[sequencechannel]==$channelID;$getServerVar[sequencestart]==1]==true]
$var[sequence;$sum[$getServerVar[sequence-currentbase];1]($getServerVar[sequence-limitbase])]

$if[$getServerVar[sequencecountlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$var[sequence]]

$if[$getServerVar[sequencelastUser]!=$authorID]
$setServerVar[sequencecountlimit;0]
$setServerVar[sequencelastUser;$authorID]
$endif

$setServerVar[sequence-currentbase-count;$var[msg]]
$setServerVar[sequence-currentbase;$sum[$getServerVar[sequence-currentbase];1]]
$setServerVar[sequencecount;$sum[$getServerVar[sequencecount];1]]
$setServerVar[sequencecountlimit;$sum[$getServerVar[sequencecountlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[mathcount];$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getVar[streak;$authorID]>$getVar[high-streak;$authorID]]
$setVar[high-streak;$getVar[streak;$authorID];$authorID]
$setVar[high-streak-Time;$getTimestamp;$authorID]
$endif
$if[$getUserVar[streak]>$getUserVar[high-streak]]
$setUserVar[high-streak;$getUserVar[streak]]
$setUserVar[high-streak-Time;$getTimestamp]
$endif

$if[$getServerVar[sequencecounthigh]<$getServerVar[sequencecount]]
$if[$getServerVar[sequencecountlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[sequencecounthigh;$sum[$getServerVar[sequencecounthigh];1]]
$setServerVar[sequence-highbase;$var[msg]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[sequencecounthigh;$sum[$getServerVar[sequencecounthigh];1]]
$setServerVar[sequence-highbase;$var[msg]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[sequencecountlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$if[$getServerVar[sequence-currentbase]>=$getServerVar[sequence-limitbase]]
$setServerVar[sequence-currentbase;0]
$setServerVar[sequence-limitbase;$sum[$getServerVar[sequence-limitbase];1]]
$endif

$if[$getVar[backwardcount;$authorID]<4]
$if[$getVar[number-countlimit;$authorID]<=$getVar[userScores;$authorID]]
  $setVar[maxcountlimit;$sum[$getVar[maxcountlimit;$authorID];1];$authorID]

  $if[$getVar[number-countlimit;$authorID]==1000]
    $setVar[number-countlimit;5000;$authorID]
    $setVar[backwardcount;1;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==5000]
    $setVar[number-countlimit;10000;$authorID]
    $setVar[backwardcount;2;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==10000]
    $setVar[number-countlimit;50000;$authorID]
    $setVar[backwardcount;3;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==50000]
    $setVar[number-countlimit;100000;$authorID]
    $setVar[backwardcount;4;$authorID]
  $endif
$endif
$endif
$else
$if[$getServerVar[sequencecooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[sequencecooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[1==$getServerVar[sequence-limitbase]]
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **1(1)**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$sum[$getServerVar[sequence-currentbase];1]($getServerVar[sequence-limitbase])**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Sequence
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sum[$getServerVar[sequence-currentbase];1]($getServerVar[sequence-limitbase])**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the next number**. Last number was **$getServerVar[sequence-currentbase]($getServerVar[sequence-limitbase])**]

$stop
$elseif[0.99<$getServerVar[sequencesave]]
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Sequence
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sum[$getServerVar[sequence-currentbase];1]($getServerVar[sequence-limitbase])**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[sequencesave;$sub[$getServerVar[sequencesave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[sequencesave];2]/$getServerVar[guildsaveslot]** saves left. **That's not the next number**. Last number was **$getServerVar[sequence-currentbase]($getServerVar[sequence-limitbase])**]


$stop
$endif
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Sequence
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sum[$getServerVar[sequence-currentbase];1]($getServerVar[sequence-limitbase])**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[sequence-limitbase;$max[$sub[$getServerVar[sequence-limitbase];1];1]]
$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[sequencecount]**!! **That's not the next number**. Next number is **1($getServerVar[sequence-limitbase])**]

$setServerVar[sequencecount;$sub[$getServerVar[sequencecount];$getServerVar[sequence-limitbase];$getServerVar[sequence-currentbase]]]
$setServerVar[sequence-currentbase;0]
$setServerVar[sequence-currentbase-count;$getServerVar[sequence-currentbase]($getServerVar[sequence-limitbase])]
$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[sequencecountlimit;0]
$setServerVar[sequencelastUser;]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif
$else
$if[$getServerVar[sequencecooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[sequencecooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[1==$getServerVar[sequence-limitbase]]
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **1(1)**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$sum[$getServerVar[sequence-currentbase];1]($getServerVar[sequence-limitbase])**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Sequence
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sum[$getServerVar[sequence-currentbase];1]($getServerVar[sequence-limitbase])**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[sequence-currentbase]($getServerVar[sequence-limitbase])**]

$stop
$elseif[0.99<$getServerVar[sequencesave]]
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Sequence
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sum[$getServerVar[sequence-currentbase];1]($getServerVar[sequence-limitbase])**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[sequencesave;$sub[$getServerVar[sequencesave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[sequencesave];2]/$getServerVar[guildsaveslot]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[sequence-currentbase]($getServerVar[sequence-limitbase])**]


$stop
$endif
$setServerVar[sequencecooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Sequence
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$sum[$getServerVar[sequence-currentbase];1]($getServerVar[sequence-limitbase])**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$setServerVar[sequence-limitbase;$max[$sub[$getServerVar[sequence-limitbase];1];1]]
$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[sequencecount]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**. Next number is **1($getServerVar[sequence-limitbase])**]

$setServerVar[sequencecount;$sub[$getServerVar[sequencecount];$getServerVar[sequence-limitbase];$getServerVar[sequence-currentbase]]]
$setServerVar[sequence-currentbase;0]
$setServerVar[sequence-currentbase-count;$getServerVar[sequence-currentbase]($getServerVar[sequence-limitbase])]
$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[sequencecountlimit;0]
$setServerVar[sequencelastUser;]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif
$elseif[$getServerVar[base64channel]==$channelID]

$if[$getServerVar[base64countlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$getServerVar[base64-base]]

$if[$getServerVar[base64lastUser]!=$authorID]
$setServerVar[base64countlimit;0]
$setServerVar[base64lastUser;$authorID]
$endif

$textSplit[ /1/2/3/4/5/6/7/8/9/a/b/c/d/e/f/g/h/i/j/k/l/m/n/o/p/q/r/s/t/u/v/w/x/y/z/A/B/C/D/E/F/G/H/I/J/K/L/M/N/O/P/Q/R/S/T/U/V/W/X/Y/Z/+/-/0/0;/]
$var[1;$splitText[$getServerVar[base64-1]]]
$var[2;$splitText[$getServerVar[base64-2]]]
$var[3;$splitText[$getServerVar[base64-3]]]
$var[4;$splitText[$getServerVar[base64-4]]]

$setServerVar[base64-base;$var[4]$var[3]$var[2]$var[1]]

$setServerVar[base64-1;$sum[$getServerVar[base64-1];1]]

$if[$getServerVar[base64-1]==65]
$setServerVar[base64-2;$sum[$getServerVar[base64-2];1]]
$endif
$if[$getServerVar[base64-1]>=66]
$setServerVar[base64-1;2]
$endif

$if[$getServerVar[base64-2]==65]
$setServerVar[base64-3;$sum[$getServerVar[base64-3];1]]
$setServerVar[base64-2;$sum[$getServerVar[base64-2];1]]
$endif
$if[$getServerVar[base64-2]>=67]
$setServerVar[base64-2;2]
$endif
$if[$getServerVar[base64-3]==65]
$setServerVar[base64-4;$sum[$getServerVar[base64-4];1]]
$setServerVar[base64-3;$sum[$getServerVar[base64-3];1]]
$endif
$if[$getServerVar[base64-3]>=67]
$setServerVar[base64-3;2]
$endif


$setServerVar[base64count;$sum[$getServerVar[base64count];1]]
$setServerVar[base64-current;$var[msg]]
$setServerVar[base64countlimit;$sum[$getServerVar[base64countlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[mathcount];$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getServerVar[base64count]>$getServerVar[base64counthigh]]
$if[$getServerVar[base64countlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[base64counthigh;$getServerVar[base64count]]
$setServerVar[base64-high;$var[msg]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[base64counthigh;$getServerVar[base64count]]
$setServerVar[base64-high;$var[msg]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[base64countlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$else
$if[$getServerVar[base64cooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[base64cooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[base64count]==0]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **1**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[base64-base]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the next number**. Last number was **$getServerVar[base64-current]**]

$stop
$elseif[0.99<$getServerVar[base64save]]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[base64save;$sub[$getServerVar[base64save];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[base64save];2]/$getServerVar[guildsaveslot]** saves left. **That's not the next number**. Last number was **$getServerVar[base64-current]**]
$stop
$endif

$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[base64count]**!! **That's not the next number**. Next number is **1**]

$addCmdReactions[❌]
$setServerVar[base64count;0]
$setServerVar[base64-base;1]
$setServerVar[base64-1;3]
$setServerVar[base64-2;1]
$setServerVar[base64-3;1]
$setServerVar[base64-4;1]
$setServerVar[base64lastUser;]
$setServerVar[base64countlimit;0]

$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$endif
$else
$if[$getServerVar[base64cooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[base64cooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[base64count]==0]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **1**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[base64-base]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[base64-current]**]

$stop
$elseif[0.99<$getServerVar[base64save]]
$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[base64save;$sub[$getServerVar[base64save];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[base64save];2]/$getServerVar[guildsaveslot]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[base64-current]**]
$stop
$endif

$setServerVar[base64cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Base-64
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[base64-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[base64count]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**. Next number is **1**]

$addCmdReactions[❌]
$setServerVar[base64count;0]
$setServerVar[base64-base;1]
$setServerVar[base64-1;3]
$setServerVar[base64-2;1]
$setServerVar[base64-3;1]
$setServerVar[base64-4;1]
$setServerVar[base64lastUser;]
$setServerVar[base64countlimit;0]

$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$endif

$endif
$catch
$stop
$endtry