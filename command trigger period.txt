$nomention
$try
$reply
$if[$and[$or[$getServerVar[decimalpointchannel]==$channelID;$getServerVar[mayanchannel]==$channelID]==true;$getVar[ban_user;$authorID]==true]==true]
$cooldown[10s;]
$addCmdReactions[⛔]
$sendMessage[⛔ <@$authorID> You have been banned from this bot.]
$stop
$endif
$textSplit[$getMessage[$channelID;$messageID;content]; ]
$var[msg;$splitText[1]]
$textSplit[$uptime;:]
$var[uptime;$splitText[2]]

$if[$channelID==$getServerVar[decimalpointchannel]]
$enableDecimals[yes]
$onlyIf[$isNumber[$var[msg]]==true;]

$var[msg;$round[$var[msg];10]]

$if[$getServerVar[decimalpoint-countlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]]

$async[decimalcode]
$if[$getServerVar[decimalpoint-lastUser]!=$authorID]
$setServerVar[decimalpoint-countlimit;0]
$setServerVar[decimalpoint-lastUser;$authorID]
$endif

$setServerVar[decimalpoint-count;$sum[$getServerVar[decimalpoint-count];1]]
$setServerVar[decimalpoint-countlimit;$sum[$getServerVar[decimalpoint-countlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[mathcount];$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getVar[streak;$authorID]>$getVar[high-streak;$authorID]]
$setVar[high-streak;$getVar[streak;$authorID];$authorID]
$setVar[high-streak-Time;$getTimestamp;$authorID]
$endif
$if[$getUserVar[streak]>$getUserVar[high-streak]]
$setUserVar[high-streak;$getUserVar[streak]]
$setUserVar[high-streak-Time;$getTimestamp]
$endif

$if[$getServerVar[decimalpoint-counthigh]<$getServerVar[decimalpoint-count]]
$if[$getServerVar[decimalpoint-countlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[decimalpoint-counthigh;$sum[$getServerVar[decimalpoint-counthigh];1]]
$setServerVar[decimalpoint-high;$round[$var[msg];10]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[decimalpoint-counthigh;$sum[$getServerVar[decimalpoint-counthigh];1]]
$setServerVar[decimalpoint-high;$round[$var[msg];10]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[decimalpoint-countlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$setServerVar[decimalpoint-current;$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]]

$if[$getServerVar[decimalpoint-current]>=$getServerVar[decimalpoint-end]]

$setServerVar[decimalpoint-current;0]
$setServerVar[decimalpoint-num;$multi[$getServerVar[decimalpoint-num];0.1]]
$setServerVar[decimalpoint-end;1]
$setServerVar[decimalpoint-end;$round[$sub[$getServerVar[decimalpoint-end];$getServerVar[decimalpoint-num]];10]]
$endif
$endasync

$if[$getVar[backwardcount;$authorID]<4]
$if[$getVar[number-countlimit;$authorID]<=$getVar[userScores;$authorID]]
  $setVar[maxcountlimit;$sum[$getVar[maxcountlimit;$authorID];1];$authorID]

  $if[$getVar[number-countlimit;$authorID]==1000]
    $setVar[number-countlimit;5000;$authorID]
    $setVar[backwardcount;1;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==5000]
    $setVar[number-countlimit;10000;$authorID]
    $setVar[backwardcount;2;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==10000]
    $setVar[number-countlimit;50000;$authorID]
    $setVar[backwardcount;3;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==50000]
    $setVar[number-countlimit;100000;$authorID]
    $setVar[backwardcount;4;$authorID]
  $endif
$endif
$endif
$else
$if[$getServerVar[decimalpoint-cooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[decimalpoint-cooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[decimalpoint-count]==0]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **0.1**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the next number**. Last number was **$getServerVar[decimalpoint-current]**]

$stop
$elseif[0.99<$getServerVar[decimalpoint-save]]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setServerVar[decimalpoint-save;$sub[$getServerVar[decimalpoint-save];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[decimalpoint-save];2]/$getServerVar[guildsaveslot]** saves left. **That's not the next number**. Last number was **$getServerVar[decimalpoint-current]**]

$stop
$endif
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[decimalpoint-count]**!! **That's not the next number**. Next number is **0.1**]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[decimalpoint-count;0]
$setServerVar[decimalpoint-current;0]
$setServerVar[decimalpoint-num;0.1]
$setServerVar[decimalpoint-end;0.9]
$setServerVar[decimalpoint-lastUser;]
$setServerVar[decimalpoint-countlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif
$else
$if[$getServerVar[decimalpoint-cooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[decimalpoint-cooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[decimalpoint-count]==0]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **0.1**.]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[decimalpoint-current]**]


$stop
$elseif[0.99<$getServerVar[decimalpoint-save]]
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[⚠️]
$setServerVar[decimalpoint-save;$sub[$getServerVar[decimalpoint-save];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[decimalpoint-save];2]/$getServerVar[guildsaveslot]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[decimalpoint-current]**]

$stop
$endif
$setServerVar[decimalpoint-cooldown;$sum[$getTimestamp;10]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[decimalpoint-count]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**. Next number is **0.1**]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Decimal Point
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$round[$sum[$getServerVar[decimalpoint-current];$getServerVar[decimalpoint-num]];10]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[decimalpoint-count;0]
$setServerVar[decimalpoint-current;0]
$setServerVar[decimalpoint-num;0.1]
$setServerVar[decimalpoint-end;0.9]
$setServerVar[decimalpoint-lastUser;]
$setServerVar[decimalpoint-countlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif




$elseif[$getServerVar[mayanchannel]==$channelID]

$if[$getServerVar[mayancountlimit]<$getVar[maxcountlimit;$authorID]]
$if[$var[msg]==$getServerVar[mayan-base]]

$if[$getServerVar[mayanlastUser]!=$authorID]
$setServerVar[mayancountlimit;0]
$setServerVar[mayanlastUser;$authorID]
$endif

$textSplit[ /./../.../..../-/-./-../-.../-..../--/--./--../--.../--..../---/---./---../---.../---....;/]
$var[1;$splitText[$getServerVar[mayan-1]]]

$textSplit[ /.\;/..\;/...\;/....\;/-\;/-.\;/-..\;/-...\;/-....\;/--\;/--.\;/--..\;/--...\;/--....\;/---\;/---.\;/---..\;/---...\;/---....\;/\;/\;;/]
$var[2;$splitText[$getServerVar[mayan-2]]]
$var[3;$splitText[$getServerVar[mayan-3]]]
$var[4;$splitText[$getServerVar[mayan-4]]]
$var[5;$splitText[$getServerVar[mayan-5]]]

$setServerVar[mayan-base;$var[5]$var[4]$var[3]$var[2]$var[1]]

$setServerVar[mayan-1;$sum[$getServerVar[mayan-1];1]]

$if[$getServerVar[mayan-1]>=21]
$setServerVar[mayan-1;1]
$setServerVar[mayan-2;$sum[$getServerVar[mayan-2];1]]
$endif
$if[$getServerVar[mayan-2]==21]
$setServerVar[mayan-3;$sum[$getServerVar[mayan-3];1]]
$setServerVar[mayan-2;$sum[$getServerVar[mayan-2];1]]
$endif
$if[$getServerVar[mayan-2]>=23]
$setServerVar[mayan-2;2]
$endif
$if[$getServerVar[mayan-3]==21]
$setServerVar[mayan-4;$sum[$getServerVar[mayan-4];1]]
$setServerVar[mayan-3;$sum[$getServerVar[mayan-3];1]]
$endif
$if[$getServerVar[mayan-3]>=23]
$setServerVar[mayan-3;2]
$endif
$if[$getServerVar[mayan-4]==21]
$setServerVar[mayan-5;$sum[$getServerVar[mayan-5];1]]
$setServerVar[mayan-4;$sum[$getServerVar[mayan-4];1]]
$endif
$if[$getServerVar[mayan-4]>=23]
$setServerVar[mayan-4;2]
$endif

$setServerVar[mayanlastUser;$authorID]
$setServerVar[mayancount;$sum[$getServerVar[mayancount];1]]
$setServerVar[mayan-base-current;$var[msg]]
$setServerVar[mayancountlimit;$sum[$getServerVar[mayancountlimit];1]]
$setVar[userCorrect;$sum[$getVar[userCorrect;$authorID];1];$authorID]
$setUserVar[userCorrect;$sum[$getUserVar[userCorrect];1]]
$setVar[userScores;$sub[$getVar[userCorrect;$authorID];$getVar[userIncorrect;$authorID]];$authorID]
$setUserVar[userScores;$sub[$getUserVar[userCorrect];$getUserVar[userIncorrect]]]
$setVar[streak;$sum[$getVar[streak;$authorID];1];$authorID]
$setUserVar[streak;$sum[$getUserVar[streak];1]]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$setServerVar[totalcurrentcount;$sum[$getServerVar[mathcount];$getServerVar[backwardcount];$getServerVar[sequencecount];$getServerVar[decimalpoint-count];$getServerVar[mayancount];$getServerVar[base64count]]]

$if[$random[1;101]==1]
$enableDecimals[yes]
$if[$getVar[save;$authorID]>$sub[$getVar[saveslot;$authorID];0.1]]
$setVar[save;$sum[$getVar[save;$authorID];$sub[$getVar[saveslot;$authorID];$getVar[save;$authorID]]];$authorID]
$else
$setVar[save;$sum[$getVar[save;$authorID];0.1];$authorID]
$endif
$endif

$if[$getVar[streak;$authorID]>$getVar[high-streak;$authorID]]
$setVar[high-streak;$getVar[streak;$authorID];$authorID]
$setVar[high-streak-Time;$getTimestamp;$authorID]
$endif
$if[$getUserVar[streak]>$getUserVar[high-streak]]
$setUserVar[high-streak;$getUserVar[streak]]
$setUserVar[high-streak-Time;$getTimestamp]
$endif

$if[$getServerVar[mayancounthigh]<$getServerVar[mayancount]]
$if[$getServerVar[mayancountlimit]<$getVar[maxcountlimit;$authorID]]
$setServerVar[mayancounthigh;$getServerVar[mayancount]]
$setServerVar[mayan-basehigh;$var[msg]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[☑️]
$else
$setServerVar[mayancounthigh;$getServerVar[mayancount]]
$setServerVar[mayan-basehigh;$var[msg]]
$setServerVar[totalhighscore;$sum[$getServerVar[mathhighscore];$getServerVar[backwardcounthigh];$getServerVar[sequencecounthigh];$getServerVar[decimalpoint-counthigh];$getServerVar[mayancounthigh];$getServerVar[base64counthigh]]]
$addCmdReactions[🛑]
$endif
$else
$if[$getServerVar[mayancountlimit]<$getVar[maxcountlimit;$authorID]]
$addCmdReactions[✅]
$else
$addCmdReactions[🛑]
$endif
$endif

$if[$getVar[backwardcount;$authorID]<4]
$if[$getVar[number-countlimit;$authorID]<=$getVar[userScores;$authorID]]
  $setVar[maxcountlimit;$sum[$getVar[maxcountlimit;$authorID];1];$authorID]

  $if[$getVar[number-countlimit;$authorID]==1000]
    $setVar[number-countlimit;5000;$authorID]
    $setVar[backwardcount;1;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==5000]
    $setVar[number-countlimit;10000;$authorID]
    $setVar[backwardcount;2;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==10000]
    $setVar[number-countlimit;50000;$authorID]
    $setVar[backwardcount;3;$authorID]
  $elseif[$getVar[number-countlimit;$authorID]==50000]
    $setVar[number-countlimit;100000;$authorID]
    $setVar[backwardcount;4;$authorID]
  $endif
$endif
$endif
$else
$if[$getServerVar[mayancooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[mayancooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[mayancount]==0]
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **.**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mayan-base]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Mayan
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[mayan-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$enableDecimals[yes]
$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. **That's not the next number**. Last number was **$getServerVar[mayan-base-current]**]
$stop
$elseif[0.99<$getServerVar[mayansave]]
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Mayan
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[mayan-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[⚠️]
$setServerVar[mayansave;$sub[$getServerVar[mayansave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[mayansave];2]/$getServerVar[guildsaveslot]** saves left. **That's not the next number**. Last number was **$getServerVar[mayan-base-current]**]
$stop
$endif
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[mayancount]**!! **That's not the next number**. Next number is **.**]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Mayan
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[mayan-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[mayancount;0]
$setServerVar[mayan-base;.]
$setServerVar[mayan-base-current;N/A]
$setServerVar[mayan-1;3]
$setServerVar[mayan-2;1]
$setServerVar[mayan-3;1]
$setServerVar[mayan-4;1]
$setServerVar[mayan-5;1]
$setServerVar[mayanlastUser;]
$setServerVar[mayancountlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif
$else
$if[$getServerVar[mayancooldown]>$getTimestamp]
$description[Channel is on cooldown **$sub[$getServerVar[mayancooldown];$getTimestamp]s**.]
$color[AC4CE4]
$stop
$elseif[$getServerVar[mayancount]==0]
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **.**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[$var[uptime2]==00]
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]
$addCmdReactions[⚠️]
$sendMessage[⚠️ <@$authorID> Next number is **$getServerVar[mayan-base]**]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$stop
$elseif[0.99<$getVar[save;$authorID]]
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Mayan
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[mayan-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[⚠️]
$setVar[save;$sub[$getVar[save;$authorID];1];$authorID]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** your saves. You have **$round[$getVar[save;$authorID];2]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[mayan-base-current]**]
$stop
$elseif[0.99<$getServerVar[mayansave]]
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]
$enableDecimals[yes]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Mayan
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[mayan-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[⚠️]
$setServerVar[mayansave;$sub[$getServerVar[mayansave];1]]
$setVar[saveused;$sum[$getVar[saveused;$authorID];1];$authorID]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setUserVar[saveused;$sum[$getUserVar[saveused];1]]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]

$sendMessage[⚠️ <@$authorID> You have used **1** guild save! There are **$round[$getServerVar[mayansave];2]/$getServerVar[guildsaveslot]** saves left. You can't count more than **$getVar[maxcountlimit;$authorID]**. Last number was **$getServerVar[mayan-base-current]**]
$stop
$endif
$setServerVar[mayancooldown;$sum[$getTimestamp;10]]

$sendMessage[❌ <@$authorID> ruined it at **$getServerVar[mayancount]**!! You can't count more than **$getVar[maxcountlimit;$authorID]**. Next number is **.**]

$sendEmbedMessage[1389118640682434682;;;;User ID: $authorID
Mention: <@$authorID>

Mode: Mayan
Server: $serverName[$guildID] ($guildID)
Link: https://discord.com/channels/$guildID/$channelID/$messageID
Message: **$var[msg]**
Next number: **$getServerVar[mayan-base]**

Global Stats:
:x: $getVar[userIncorrect;$authorID]
Save: $round[$getVar[save;$authorID];2]
Save Used: $getVar[saveused;$authorID]
Streak: $getVar[streak;$authorID]

Stats in `$serverName[$guildID]`
:x: $getUserVar[userIncorrect]
Save Used: $getUserVar[saveused]
Streak: $getUserVar[streak];#AC4CE4;$username[$authorID];$userAvatar[$authorID];/help]

$addCmdReactions[❌]
$setVar[userIncorrect;$sum[$getVar[userIncorrect;$authorID];1];$authorID]
$setUserVar[userIncorrect;$sum[$getUserVar[userIncorrect];1]]
$setServerVar[mayancount;0]
$setServerVar[mayan-base;.]
$setServerVar[mayan-base-current;N/A]
$setServerVar[mayan-1;3]
$setServerVar[mayan-2;1]
$setServerVar[mayan-3;1]
$setServerVar[mayan-4;1]
$setServerVar[mayan-5;1]
$setServerVar[mayanlastUser;]
$setServerVar[mayancountlimit;0]
$setVar[streak;0;$authorID]
$setUserVar[streak;0]
$setVar[lastActive;$getTimestamp;$authorID]
$setUserVar[lastActive;$getTimestamp]
$endif

$endif
$catch
$stop
$endtry